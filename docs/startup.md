# 本地开发运行

1. 准备 ansible 本地运行环境
   ```shell
   make setup-ansible
   ```

2. 加载 python venv
    ```shell
    source .venv/bin/activate
    ```

3. 运行 ansible playbook
    ```shell
    ansible-playbook -i inventory/inventory.ini init-setup.yml
    ansible-playbook -i inventory/inventory.ini deploy-extensions.yml
    ```

# 离线包解压运行

1. 获取离线包

2. 解压离线包，进入解压后的目录

3. 加载 python venv
   ```shell
   source .venv/bin/activate
   ```

4. 运行 ansible playbook
    ```shell
    ansible-playbook -i inventory/inventory.ini init-setup.yml
    ansible-playbook -i inventory/inventory.ini deploy-extensions.yml
    ```