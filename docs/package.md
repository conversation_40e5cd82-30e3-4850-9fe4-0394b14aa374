# 安装包构建操作说明文档

[[_TOC_]]

## 命令行手动构建安装包

1. 导出必要的环境变量，如：
   ```shell
   export ARCHTARGET_ARCH=x86_64
   ```
2. 执行构建，如下包将会输出在当前目录下的 `pack` 目录下
   ```shell
   make PACKAGE_STORE_DIR=./pack package
   ```

## Gitlab CI/CD 构建安装包

本节基于 Gitlab CI/CD 构建流程，详细说明如何在 Gitlab CI/CD 中构建安装包。

1. 进入 CI/CD -> pipleline 页面，点击 `Run Pipeline`

2. 选择 `main` 分支，填入以下 pipeline variables：
   ![gitlab-pipeline](./imgs/package-gitlab-pipeline.png)

3. 点击 `Run Pipeline`，等待构建完成

4. 构建好的包将可以通过 job 页面右侧 `Job artifacts` 下载
