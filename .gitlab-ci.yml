default:
  image: release-ci.daocloud.io/common-ci/common-ci-builder:v0.1.126
  tags:
  - docker
  retry:
    max: 2
    when:
    - runner_system_failure
    - stuck_or_timeout_failure
    - scheduler_failure

stages:
- build
- release
- package

image-build:
  rules:
  - if: '$CI_PIPELINE_SOURCE == "web"'
    when: never
  - if: '$CI_PIPELINE_SOURCE == "merge_request_event" || $CI_COMMIT_BRANCH == "main"'
  retry:
    max: 2
  stage: build
  script:
  - make image
  interruptible: true

release-version:
  rules:
  - if: '$CI_PIPELINE_SOURCE == "web" && $ACTION != "package"'
    when: always
  - when: never
  stage: release
  script:
  - make release-version
  interruptible: true

package:
  stage: package
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web" && $ACTION == "package"'
      when: always
    - when: never
  tags:
    - docker
  artifacts:
    paths:
      - pack/korca-*.tgz
  script:
    - make PACKAGE_STORE_DIR=./pack package
  interruptible: true
