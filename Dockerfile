FROM alpine:3.19 AS builder

ARG TZ=Etc/UTC

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
  && apk add --update --no-cache python3 py3-pip openssh curl sshpass rsync bash \
  gcc musl-dev python3-dev libffi-dev git patch

WORKDIR /korca

COPY . .

ENV LANG=C.UTF-8
ENV PATH="/venv/bin:$PATH"

RUN python3 -m venv /venv \
  && pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple \
  && pip install --no-cache-dir pip -U \
  && pip install --no-cache-dir -r requirements.txt

FROM scratch

COPY --from=builder / /

ENV ANSIBLE_CONFIG=/korca/ansible.cfg
ENV PATH="/venv/bin:$PATH"

WORKDIR /korca
