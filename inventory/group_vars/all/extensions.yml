---
# Extension components configuration variables

mx_exporter_version: 0.10.2
mx_exporter_replicaCount: 1


# Network Configuration
spiderpool_image_repo: xxxxxxxx
spiderpool_image_tag: xxx

mellanox_ofed_driver_version: 24.01-*******
mellanox_ofed_driver_download_url: xxxxxxxxxxxxxxxx

set_nic_rdma_mode_download_url: xxxxxxxxxxxxxxx
set_nic_addr_download_url: xxxxxxxxxxxxxxxx

rdma_qos_download_url: xxxxxxxxxxxxxxxxx
all_rdma_nics: true
gpu_rdma_priority: 5
gpu_cnp_priority: 6

spider_ip_pools:
- name: net1
  gateway: **********
  ip_version: 4
  ips: [************-************]
  subnet: **********/24
- name: net2
  gateway: **********
  ip_version: 4
  ips: [************-************]
  subnet: **********/24
- name: net3
  gateway: **********
  ip_version: 4
  ips: [************-************]
  subnet: **********/24
- name: net4
  gateway: **********
  ip_version: 4
  ips: [************-************]
  subnet: **********/24
  
spidernet_chat_repo: xxxxxxxxxxxx
rdma_tools_values: |-
    extraAnnotations:
      cni.spidernet.io/rdma-resource-inject: rdma-network
    hostnetwork: false
    image:
      registry: {{ image_registry }}/ghcr.m.daocloud.io
      tag: 7cf2d54260cf188a44766409339803d24eb31356
    securityContext:
      capabilities:
        add:
        - IPC_LOCK
      privileged: true
      
vf_nums: 8


# Storage Configuration
csi_provisioner_image_repo: xxxxxxxxxx
csi_provisioner_image_tag: xxxxxxx
csi_livenessprobe_image_repo: xxxxxxxxxxx
csi_livenessprobe_image_tag: xxxxxx
csi_node_driver_registrar_image_repo: xxxxxx
csi_node_driver_registrar_image_tag: xxx
azurelustre_csi_driver_image_repo: xxxxxx
azurelustre_csi_driver_image_tag: xxxxxx

csi_azurelustre_controller_replicas: 2

azurelustre_storageclass_mgs_ip_address: ***********@o2ib
