---
# Initial setup configuration variables
# Common variables
_host_arch_groups:
  x86_64: amd64
  aarch64: arm64
  armv7l: arm

arch: "{{ ansible_architecture }}"
deb_arch: "{{ _host_arch_groups[ansible_architecture] }}"

# Package versions
metax_driver_version: *********
maca_sdk_version: ********
mxspm_version: 0.6.0
metax_gpu_k8s_package_version: 0.10.2
mx_exporter_version: 0.10.2

metax_driver_file_name: metax-driver-mxc500-{{ metax_driver_version }}-deb-{{ arch }}.run
maca_sdk_file_name: maca-sdk-mxc500-{{ maca_sdk_version }}-deb-{{ arch }}.tar.xz
mxspm_file_name: mxspm_{{ mxspm_version }}_{{ deb_arch }}.deb
metax_gpu_k8s_package_file_name: metax-gpu-k8s-package.{{ metax_gpu_k8s_package_version }}.tar.gz
mx_exporter_file_name: mx-exporter-{{ mx_exporter_version }}.tgz

# RDMA network interfaces configuration
# Each interface configuration should include:
# - interface: network interface name
# - ipv4_ip: IPv4 address with CIDR notation
# - ipv4_gateway: IPv4 gateway address
# - mtu: Maximum Transmission Unit
# - enable_policy_route: enable policy routing (true/false)

rdma_network_interfaces: []

# rdma_network_interfaces:
#   gpu-node-01:
#     - interface: "ens1np0"
#       ipv4_ip: "**********09/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#     - interface: "ens108np0"
#       ipv4_ip: "**********09/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens110np0"
#       ipv4_ip: "**********09/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens841np0"
#       ipv4_ip: "**********09/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens842np0"
#       ipv4_ip: "**********09/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#   gpu-node-02:
#     - interface: "ens1np0"
#       ipv4_ip: "************/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#     - interface: "ens108np0"
#       ipv4_ip: "************/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens110np0"
#       ipv4_ip: "************/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens841np0"
#       ipv4_ip: "**********10/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens842np0"
#       ipv4_ip: "**********10/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#   gpu-node-03:
#     - interface: "ens1np0"
#       ipv4_ip: "**********11/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#     - interface: "ens108np0"
#       ipv4_ip: "**********11/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens110np0"
#       ipv4_ip: "**********11/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens841np0"
#       ipv4_ip: "**********11/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens842np0"
#       ipv4_ip: "**********11/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#   gpu-node-04:
#     - interface: "ens1np0"
#       ipv4_ip: "**********12/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#     - interface: "ens108np0"
#       ipv4_ip: "**********12/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens110np0"
#       ipv4_ip: "**********12/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens841np0"
#       ipv4_ip: "**********12/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true
#     - interface: "ens842np0"
#       ipv4_ip: "**********12/24"
#       ipv4_gateway: "**********"
#       mtu: "4200"
#       enable_policy_route: true

# RDMA QoS configuration for lossless network
# GPU_RDMA_PRIORITY: RDMA traffic priority queue (0-7, default: 5)
# GPU_CNP_PRIORITY: CNP packet priority queue (0-7, default: 6)

rdma_qos_priority: "5"
cnp_qos_priority: "6"

# Reset all RDMA network configurations
# If set to true, it will reset all RDMA configurations including QoS, NIC modes
# and exclusive mode. This is useful when re-provisioning the network.
reset_all_network_config: false
