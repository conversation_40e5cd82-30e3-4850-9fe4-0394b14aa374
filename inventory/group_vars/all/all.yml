---
# Global configuration variables for GPU cluster deployment

# Ansible configuration
ansible_user: root
ansible_ssh_common_args: '-o StrictHostKeyChecking=no'

# Local path of the required packages
packages_local_path: "{{ playbook_dir }}/packages"
# Remote store path for packages, used in remote execution scenarios
packages_remote_path: "/opt/packages"

reboot_connect_timeout: 15
reboot_timeout: 900

# Common packages
essential_packages:
  - curl
  - wget
  - unzip
  - git
  - vim

image_arch: "{{ host_architecture | default('amd64') }}"
image_pull_policy: IfNotPresent

image_registry: "{{ image_registry | default('docker.io') }}"

maca_sdk_download_url: "xxxxxxxxxxxx"
metax_driver_download_url: "xxxxxxxxxx"
mxvbios_download_url: "xxxxxxxxxxxxx"
metax_gpu_k8s_package_download_url: "xxxxxxxxxxxxx"
mx_exporter_download_url: "xxxxxxxxxxxxx"

maca_sdk_binary_checksum: "{{ maca_sdk_checksums[image_arch][maca_sdk_version] }}"
metax_driver_binary_checksum: "{{ metax_driver_checksums[image_arch][metax_driver_version] }}"
mxvbios_binary_checksum: "{{ mxvbios_checksums['none'][mxvbios_version] }}"
metax_gpu_k8s_package_binary_checksum: "{{ metax_gpu_k8s_package_checksums['none'][metax_gpu_k8s_package_version] }}"
mx_exporter_binary_checksum: "{{ mx_exporter_checksums['none'][mx_exporter_version] }}"

# K8s Initialization Options
helm_enabled: true
kube_network_plugin: calico
calico_ip_auto_method: kubernetes-internal-ip
calico_ip6_auto_method: kubernetes-internal-ip
