# GPU Cluster Inventory Configuration

[control_plane_nodes]
control-plane-01 ansible_host=*************

[gpu_nodes]
gpu-node-01 ansible_host=*************
gpu-node-02 ansible_host=*************
gpu-node-03 ansible_host=*************
gpu-node-04 ansible_host=*************

[all:vars]
ansible_user=root
ansible_ssh_private_key_file=~/.ssh/id_rsa
ansible_ssh_common_args='-o StrictHostKeyChecking=no'
; ansible_password=dangerous
; ansible_port=22
