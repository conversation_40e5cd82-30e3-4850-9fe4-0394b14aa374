#!/usr/bin/env bash

set -e

PYTHON_VERSION=$1
TARGET_ARCH=$2
CONTEXT_PATH=$3

python_version_minor=$(echo $PYTHON_VERSION | cut -d. -f1,2)
python_version_minor_nodot=${python_version_minor/./}

venv_path=$CONTEXT_PATH/.venv

if [ -f $CONTEXT_PATH/bin/python.$TARGET_ARCH ] || [ -f $venv_path/bin/python.$TARGET_ARCH ]; then
  echo "[INFO] Python already exists: $CONTEXT_PATH/bin/python"
else 
  echo "[INFO] Downloading Python: $PYTHON_VERSION"
  mkdir -p $CONTEXT_PATH/bin
  curl -L https://files.m.daocloud.io/github.com/niess/python-appimage/releases/download/python$python_version_minor/python$PYTHON_VERSION-cp$python_version_minor_nodot-cp$python_version_minor_nodot-manylinux2014_$TARGET_ARCH.AppImage -o $CONTEXT_PATH/bin/python.$TARGET_ARCH

  chmod +x $CONTEXT_PATH/bin/python.$TARGET_ARCH
fi

if [ -d $venv_path ]; then
  echo "[INFO] Virtual environment already exists: $venv_path"
else
  echo "[INFO] Creating virtual environment: $venv_path"
  $CONTEXT_PATH/bin/python.$TARGET_ARCH -m venv --symlinks $venv_path
fi

if [ -f $CONTEXT_PATH/bin/python.$TARGET_ARCH ]; then
  echo "[INFO] Re-link python symlink"
  mv $CONTEXT_PATH/bin/python.$TARGET_ARCH $venv_path/bin
  ln -sf python.$TARGET_ARCH $venv_path/bin/python
  rm -rf $CONTEXT_PATH/bin
fi

if [ -f $venv_path/bin/ansible-playbook ]; then
  echo "[INFO] ansible-playbook already exists: $venv_path/bin/ansible-playbook"
else
  echo "[INFO] Installing Python dependencies from requirements.txt"
  source $venv_path/bin/activate
  $CONTEXT_PATH/.venv/bin/pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple -r $CONTEXT_PATH/requirements.txt
fi


echo "[INFO] Patch activate script"
[[ "$(head -1 $venv_path/bin/activate)" == venv_bin_path=* ]] || sed -i '1ivenv_bin_path=$(realpath $(dirname ${BASH_SOURCE[0]})); find $venv_bin_path -name "ansible*" -exec sed -i "s@^#!.*@#!$venv_bin_path/python@" {} \\;' $venv_path/bin/activate
sed -i "s|$venv_path|\$(dirname \$venv_bin_path)|" $venv_path/bin/activate
