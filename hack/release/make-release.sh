#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail
set -ex

CUR_DIR=$(
  cd -- "$(dirname "$0")" >/dev/null 2>&1
  pwd -P
)

if [ -z "${ACTION}" ]; then
  echo you must specify ACTION var >>/dev/stderr
  exit 1
fi

if [ "${ACTION}" != "RELEASE" ]; then
  echo "WHEN MAKE A RELEASE, THE ACTION MUST BE RELEASE" >>/dev/stderr
  exit 1
fi

if [ "${CI_COMMIT_BRANCH}" == "main" ]; then
  echo "Creating a new relase can not base on main" >>/dev/stderr
  exit 1
fi

GITLAB_REPO='gitlab.daocloud.cn/ndx/engineering/infrastructure/korca.git'

get_version() {
  bash ${CUR_DIR}/get-version.sh $1
}

# 若获取的当前版本是 candidate 版本，则需更新为 release 版本
# 若获取的当前版本是 release 版本，则无需处理
get_current_version() {
  candidate_tag="-rc"
  version=$(get_version ${1})
  if [[ "${version}" == *"${candidate_tag}"* ]]; then
    release_version=$(echo ${version} | awk -F ${candidate_tag} '{print $1}')
    echo "${release_version}"
  else
    echo "${version}"
  fi
}

bump_patch_version() {
  main_version=$(echo $1 | awk -F . '{print $1}')
  minor_version=$(echo $1 | awk -F . '{print int($2)}')
  patch_version=$(echo $1 | awk -F . '{print int($3)}')
  echo "${main_version}.${minor_version}.$((${patch_version} + 1))"
}

# 获取当前版本号之前的版本
# 如果是 minor 发版(v0.11.0) 上一个版本则是 v0.10.0 (y-1)
# 如果是 patch 发版(v0.11.1) 上一个版本为 v0.11.0(z-1)
pre_version() {
  main_version=$(echo $1 | awk -F . '{print $1}')
  minor_version=$(echo $1 | awk -F . '{print int($2)}')
  patch_version=$(echo $1 | awk -F . '{print int($3)}')
  pre_patch="$((${patch_version} - 1))"
  if [ "${pre_patch}" -lt 0 ]; then
    echo "${main_version}.$((${minor_version} - 1)).0"
  else
    echo "${main_version}.${minor_version}.$((${patch_version} - 1))"
  fi
}

git fetch

CUR_VERSION=$(get_current_version korca)
MAJOE_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $1}')
MINOR_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $2}')
PATCH_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $3}')
SHORT_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $1"."$2}')
CI_BRANCH_VERSION=$(echo ${CI_COMMIT_BRANCH} | awk -F - '{print $2}')
NEXT_PATCH_VERSION=$(bump_patch_version ${CUR_VERSION})
PRE_VERSION=$(pre_version ${CUR_VERSION})
echo "current version is ${CUR_VERSION}"
echo "new patch version: ${NEXT_PATCH_VERSION}"
echo "pre version: ${NEXT_PATCH_VERSION}"

if [ "${SHORT_VERSION}" != "${CI_BRANCH_VERSION}" ]; then
  echo "CI_BRANCH version ${CI_BRANCH_VERSION} is inconsistent with the versions.json file ${CUR_VERSION}, please check it carefully" >>/dev/stderr
  exit 1
fi

if [ -n "${CI_BUILD_REF_NAME}" ]; then
  git checkout ${CI_BUILD_REF_NAME}
fi

f=$(mktemp)
jq ".korca = \"${NEXT_PATCH_VERSION}\"" ${CUR_DIR}/../../versions.json >$f && cat $f >${CUR_DIR}/../../versions.json
rm $f

git add ${CUR_DIR}/../..

if ! git config user.name; then
  git config user.name "Auto Release Bot"
  git config user.email "<EMAIL>"
fi

git commit -m "Release ${CUR_VERSION} and modify versions.json"
git tag -a ${CUR_VERSION} -m "Release ${CUR_VERSION}"

if [ -n "${GITLAB_TOKEN}" ]; then
  git remote set-url origin https://gitlab-ci-token:${GITLAB_TOKEN}@${GITLAB_REPO}
fi

if [ -z "${CI_BUILD_REF_NAME}" ]; then
  git push origin $(git rev-parse --abbrev-ref HEAD)
else
  git push origin ${CI_BUILD_REF_NAME}
fi

git push origin ${CUR_VERSION}

echo "CI_PROJECT_ID: ${CI_PROJECT_ID}"

curl -s -v \
  -H "PRIVATE-TOKEN: ${GITLAB_TOKEN}" \
  -H 'Content-Type: application/json' \
  "https://gitlab.daocloud.cn/api/v4/projects/${CI_PROJECT_ID}/releases" \
  -X POST \
  -d "$(echo '{}' | jq \
    --arg name "Release ${CUR_VERSION}" \
    --arg tag_name "${CUR_VERSION}" \
    '.name = $name | .tag_name = $tag_name | .description = $description')"
