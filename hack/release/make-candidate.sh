#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail
set -ex

CUR_DIR=$(
  cd -- "$(dirname "$0")" >/dev/null 2>&1
  pwd -P
)

if [ -z "${ACTION}" ]; then
  echo you must specify ACTION var >>/dev/stderr
  exit 1
fi

if [ "${ACTION}" != "CANDIDATE" ]; then
  echo "WHEN MAKE A CANDIDATE, THE ACTION MUST BE CANDIDATE" >>/dev/stderr
  exit 1
fi

if [ "${CI_COMMIT_BRANCH}" == "main" ]; then
  echo "Creating a new relase can not base on main" >>/dev/stderr
  exit 1
fi

GITLAB_REPO='gitlab.daocloud.cn/ndx/engineering/infrastructure/korca.git'

get_version() {
  bash ${CUR_DIR}/get-version.sh $1
}

# 若获取的当前版本是 candidate 版本，则无需处理
# 若获取的当前版本是 release 版本，则需更新为 candidate 版本 rc1
get_current_version() {
  candidate_tag="-rc"
  version=$(get_version ${1})
  if [[ "${version}" == *"${candidate_tag}"* ]]; then
    echo "${version}"
  else
    echo "${version}-rc1"
  fi
}

# 若获取的当前版本是 candidate 版本，则 rc 值加1
# 若获取的当前版本是 release 版本，则需更新为 candidate 版本 rc2
bump_candidate_version() {
  candidate_tag="-rc"
  if [[ "${1}" == *"${candidate_tag}"* ]]; then
    release_version=$(echo ${1} | awk -F ${candidate_tag} '{print $1}')
    rc_version=$(echo ${1} | awk -F ${candidate_tag} '{print $2}')
    echo "${release_version}-rc$((${rc_version} + 1))"
  else
    echo "${1}-rc2"
  fi
}

git fetch

CUR_VERSION=$(get_current_version korca)
SHORT_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $1"."$2}')
CI_BRANCH_VERSION=$(echo ${CI_COMMIT_BRANCH} | awk -F - '{print $2}')
NEXT_CANDIDATE_VERSION=$(bump_candidate_version ${CUR_VERSION})
echo "current version is ${CUR_VERSION}"
echo "new candidate version: ${NEXT_CANDIDATE_VERSION}"

if [ "${SHORT_VERSION}" != "${CI_BRANCH_VERSION}" ]; then
  echo "CI_BRANCH version ${CI_BRANCH_VERSION} is inconsistent with the versions.json file ${CUR_VERSION}, please check it carefully" >>/dev/stderr
  exit 1
fi

if [ -n "${CI_BUILD_REF_NAME}" ]; then
  git checkout ${CI_BUILD_REF_NAME}
fi

f=$(mktemp)
jq ".korca = \"${NEXT_CANDIDATE_VERSION}\"" ${CUR_DIR}/../../versions.json >$f && cat $f >${CUR_DIR}/../../versions.json
rm $f

git add ${CUR_DIR}/../..

if ! git config user.name; then
  git config user.name "Auto Release Bot"
  git config user.email "<EMAIL>"
fi

git commit -m "Release candidate ${CUR_VERSION} and modify versions.json"
git tag -a ${CUR_VERSION} -m "Release candidate ${CUR_VERSION}"

if [ -n "${GITLAB_TOKEN}" ]; then
  git remote set-url origin https://gitlab-ci-token:${GITLAB_TOKEN}@${GITLAB_REPO}
fi

if [ -z "${CI_BUILD_REF_NAME}" ]; then
  git push origin $(git rev-parse --abbrev-ref HEAD)
else
  git push origin ${CI_BUILD_REF_NAME}
fi

git push origin ${CUR_VERSION}
