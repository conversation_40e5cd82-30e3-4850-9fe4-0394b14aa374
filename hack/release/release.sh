#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail

CUR_DIR=$(
  cd -- "$(dirname "$0")" >/dev/null 2>&1
  pwd -P
)

if [ -z "${ACTION}" ]; then
  echo you must specify ACTION var >>/dev/stderr
  exit 1
fi

if [ "${ACTION}" == "NEW_BRANCH" ]; then
  bash ${CUR_DIR}/make-branch.sh
elif [ "${ACTION}" == "CANDIDATE" ]; then
  bash ${CUR_DIR}/make-candidate.sh
elif [ "${ACTION}" == "RELEASE" ]; then
  bash ${CUR_DIR}/make-release.sh
else
  echo "ACTION IS NOT INVALID ${ACTION}" >>/dev/stderr
  exit 1
fi
