#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail

CUR_DIR=$(
  cd -- "$(dirname "$0")" >/dev/null 2>&1
  pwd -P
)

if [ -z "${ACTION}" ]; then
  echo you must specify ACTION var >>/dev/stderr
  exit 1
fi

if [ "${ACTION}" != "NEW_BRANCH" ]; then
  echo "WHEN CREATING A BRANCH, THE ACTION MUST BE NEW_BRANCH" >>/dev/stderr
  exit 1
fi

if [ "${CI_COMMIT_BRANCH}" != "main" ]; then
  echo "Creating a new branch can only be based on the main branch, and the current branch is ${CI_COMMIT_BRANCH}" >>/dev/stderr
  exit 1
fi

GITLAB_REPO='gitlab.daocloud.cn/ndx/engineering/infrastructure/korca.git'

get_version() {
  bash ${CUR_DIR}/get-version.sh $1
}

bump_minor_version() {
  main_version=$(echo $1 | awk -F . '{print $1}')
  minor_version=$(echo $1 | awk -F . '{print int($2)}')
  patch_version=$(echo $1 | awk -F . '{print int($3)}')
  echo "${main_version}.$((${minor_version} + 1)).${patch_version}"
}

bump_patch_version() {
  main_version=$(echo $1 | awk -F . '{print $1}')
  minor_version=$(echo $1 | awk -F . '{print int($2)}')
  patch_version=$(echo $1 | awk -F . '{print int($3)}')
  echo "${main_version}.${minor_version}.$((${patch_version} + 1))"
}

CUR_VERSION=$(get_version korca)
MAJOE_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $1}')
MINOR_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $2}')
PATCH_VERSION=$(echo ${CUR_VERSION} | awk -F . '{print $3}')

if [ "${PATCH_VERSION}" != "0" ]; then
  echo "checkout a new branch, the current Semer version patch number needs to be equal to 0,current version is ${CUR_VERSION}" >>/dev/stderr
  exit 1
fi

echo "current version is ${CUR_VERSION}"

NEXT_MINOR_VERSION=$(bump_minor_version ${CUR_VERSION})
echo "new minor version: ${NEXT_MINOR_VERSION}"

NEXT_PATCH_VERSION=$(bump_patch_version ${CUR_VERSION})
echo "new patch version: ${NEXT_PATCH_VERSION}"

# checkout new branch
BRANCH_NAME=$(echo ${CUR_VERSION} | awk -F . '{print "release-"$1"."$2}')

echo "new branch is ${BRANCH_NAME}"

if ! git config user.name; then
  git config user.name "Auto Release Bot"
  git config user.email "<EMAIL>"
fi

git checkout -b ${BRANCH_NAME}

if [ -n "${GITLAB_TOKEN}" ]; then
  git remote set-url origin https://gitlab-ci-token:${GITLAB_TOKEN}@${GITLAB_REPO}
fi

git add ${CUR_DIR}/../..

git push origin ${BRANCH_NAME}

# check out main branch and put a pr for bump minor version

git checkout main

git checkout -b bump-${NEXT_MINOR_VERSION}

# first commit for new branch, update the versions.json bump the patch version
f=$(mktemp)
jq ".korca = \"${NEXT_MINOR_VERSION}\"" ${CUR_DIR}/../../versions.json >$f && cat $f >${CUR_DIR}/../../versions.json
rm $f

git add ${CUR_DIR}/../..

git commit -m "check new branch and modify versions.json"

git push origin bump-${NEXT_MINOR_VERSION}

curl -s -v \
  -H "PRIVATE-TOKEN: ${GITLAB_TOKEN}" \
  -H 'Content-Type: application/json' \
  "https://gitlab.daocloud.cn/api/v4/projects/${CI_PROJECT_ID}/merge_requests" \
  -X POST \
  -d "$(echo '{}' | jq --arg title "bump main version to ${NEXT_MINOR_VERSION}" \
    --arg source_branch "bump-${NEXT_MINOR_VERSION}" \
    --arg target_branch "main" \
    --arg description "/kind others     /skip-pipeline" \
    '.title = $title | .source_branch = $source_branch | .target_branch = $target_branch | .description = $description')"
