#!/usr/bin/env bash

set -e

package_name=$1
archive_dir_root=$2
package_store_dir=$3

archive_dir_root_updir=$(dirname $archive_dir_root)
archive_dir_name=$(basename $archive_dir_root)

include_path=(
  .venv
  inventory
  packages
  playbooks
  roles
  ansible.cfg
  deploy-extensions.yml
  init-setup.yml
  README.md
  requirements.txt
)

include_path_str=""
for path in "${include_path[@]}"; do
  echo "[INFO] Including $path to final package"
  include_path_str+=" $archive_dir_name/$path"
done

mkdir -p $package_store_dir

tar -C $archive_dir_root_updir -czf $package_store_dir/$package_name $include_path_str
echo "[INFO] Created package $package_store_dir/$package_name"