---
- name: Check mx-smi command
  ansible.builtin.command: which mx-smi 
  register: mx_smi_exist_check
  ignore_errors: true

- name: Get mx-smi output
  ansible.builtin.command: mx-smi
  register: mx_smi_output
  failed_when: mx_smi_output.rc != 0 and mx_smi_output.rc != 26
  when: mx_smi_exist_check.rc == 0

- name: Get driver version in metax driver package
  ansible.builtin.shell: "{{ packages_remote_path }}/{{ metax_driver_file_name }} --list | grep -o 'metax-linux_.*.deb' | sed -r 's/^metax-linux_(.*)-.*.deb$/\\1/'"
  register: driver_version_in_package

- name: Set mx-smi driver version expect
  ansible.builtin.set_fact:
    mx_smi_driver_version_expect: "Kernel Mode Driver Version: {{ driver_version_in_package.stdout }}"

- name: Check if GPU driver need to reinstall
  ansible.builtin.set_fact:
    gpu_driver_need_install: "{{ mx_smi_driver_version_expect not in mx_smi_output.stdout }}"
  when: mx_smi_exist_check.rc == 0

- name: Install GPU driver
  #when : mx_smi_exist_check.rc != 0 or gpu_driver_need_install
  when : mx_smi_exist_check.rc != 0
  block:
  - name: Copy GPU driver to the node
    ansible.builtin.copy:
      src: "{{ packages_local_path }}/{{ metax_driver_file_name }}"
      dest: "{{ packages_remote_path }}/{{ metax_driver_file_name }}"
      mode: '0755'
      owner: root
      group: root
    register: copy_metax_driver
  
  - name: Install GPU driver
    ansible.builtin.command: "{{ copy_metax_driver.dest }} -- -f -p topo_df=1 -p disable_pci_cpl_timeout=1"
    become: true
  
  # Reboot the node if the driver is the first installation
  - name: Reboot the node
    ansible.builtin.reboot:
      msg: "Rebooting to complete GPU driver installation"
      connect_timeout: "{{ reboot_connect_timeout }}"
      reboot_timeout: "{{ reboot_timeout }}"
    when: mx_smi_exist_check.rc != 0
  
  - name: Check if GPU driver version is consistent
    ansible.builtin.command: mx-smi
    register: check_driver_result
    failed_when: "mx_smi_driver_version_expect not in check_driver_result.stdout"
    when: copy_metax_driver is changed
