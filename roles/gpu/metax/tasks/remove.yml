---
# TODO
- name: Check if GPU driver needs to be removed
  ansible.builtin.command: which mx-smi
  register: gpu_driver_check
  ignore_errors: true

- name: Set fact to indicate whether to unload GPU driver
  ansible.builtin.set_fact:
    unload_gpu_driver: "{{ gpu_driver_check.rc == 0 }}"

- name: Unload GPU driver if it is loaded
  when: unload_gpu_driver
  block:
    - name: Unload GPU driver
      ansible.builtin.command: /opt/mxdriver/mxdriver-install.sh -U
      become: true

    # TODO
    - name: Check if reboot is required
      ansible.builtin.set_fact:
        reboot_required: false

    - name: Reboot if required
      ansible.builtin.reboot:
        msg: "Rebooting to complete GPU driver removal"
        connect_timeout: "{{ reboot_connect_timeout }}"
        reboot_timeout: "{{ reboot_timeout }}"
      when: reboot_required
