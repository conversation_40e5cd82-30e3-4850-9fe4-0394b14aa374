---
- name: Copy mxspm package to target host
  ansible.builtin.copy:
    src: "{{ packages_local_path }}/{{ mxspm_file_name }}"
    dest: "{{ packages_remote_path }}/{{ mxspm_file_name }}"
  register: copy_mxspm_result

- name: Install mxspm package
  ansible.builtin.apt:
    deb: "{{ copy_mxspm_result.dest }}"
    state: present
    force: true

- name: Ensure mxspm service is enabled and started
  ansible.builtin.systemd:
    name: mxspm
    enabled: true
    state: started
