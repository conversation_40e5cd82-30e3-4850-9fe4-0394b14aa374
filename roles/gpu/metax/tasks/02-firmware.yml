---
- name: Get mx-smi output
  ansible.builtin.command: mx-smi
  failed_when: mx_smi_output.rc != 0 and mx_smi_output.rc != 26
  register: mx_smi_output

- name: Find GPU firmware file on the node
  ansible.builtin.find:
    paths: "/lib/firmware/metax/mxc500"
    patterns: "mxvbios-*-C550.bin"
    file_type: file
  failed_when: gpu_firmware_file.matched != 1
  register: gpu_firmware_file
  
- name: Set GPU firmware version expect
  ansible.builtin.set_fact:
    gpu_firmware_version_expect: "BIOS Version: {{ gpu_firmware_file.files[0].path | regex_search('mxvbios-(.+?)-.*', '\\1') | first }}"

- name: Check if GPU firmware need to reinstall
  ansible.builtin.set_fact:
    gpu_firmware_need_install: "{{ gpu_firmware_version_expect not in mx_smi_output.stdout }}"

- name: Install GPU firmware
  when: gpu_firmware_need_install
  block:
  - name: Install GPU firmware
    ansible.builtin.command: mx-smi -u {{ gpu_firmware_file.files[0].path }} -t 600
    failed_when: "'vbios-upgrade Done' not in install_firmware_result.stdout"
    register: install_firmware_result
    become: true
  
  - name: Reboot the node
    ansible.builtin.reboot:
      msg: "Rebooting to complete GPU firmware installation"
      connect_timeout: "{{ reboot_connect_timeout }}"
      reboot_timeout: "{{ reboot_timeout }}"
  
  - name: Check if GPU firmware version is consistent
    ansible.builtin.command: mx-smi
    failed_when: "gpu_firmware_version_expect not in check_firmware_result.stdout"
    register: check_firmware_result
