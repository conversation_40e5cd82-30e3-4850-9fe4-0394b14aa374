---
- name: Display common configuration start
  debug:
    msg: "Starting common configuration on {{ inventory_hostname }}"

- name: Update system packages
  debug:
    msg: "Updating system packages on {{ inventory_hostname }}"

- name: Install essential packages
  debug:
    msg: "Installing essential packages: {{ essential_packages | default(['curl', 'wget', 'unzip']) | join(', ') }}"

- name: Configure system settings
  debug:
    msg: "Configuring system settings on {{ inventory_hostname }}"

- name: Setup user permissions
  debug:
    msg: "Setting up user permissions for {{ ansible_user }}"

- name: Display common configuration completion
  debug:
    msg: "Common configuration completed on {{ inventory_hostname }}"

- name: Ensure remote package store directory exists
  ansible.builtin.file:
    path: "{{ packages_remote_path }}"
    state: directory
    mode: "0755"