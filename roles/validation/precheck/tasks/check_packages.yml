---
- name: Check if required package files exist
  ansible.builtin.stat:
    path: "{{ packages_local_path }}/{{ item }}"
  loop: "{{ required_packages }}"
  register: package_files
  delegate_to: localhost
  run_once: true

- name: Fail if any required package is missing
  ansible.builtin.fail:
    msg: "Required package '{{ item.item }}' not found in '{{ packages_local_path }}'. Please ensure all required packages are present before proceeding."
  when: not item.stat.exists
  loop: "{{ package_files.results }}"
  loop_control:
    label: "{{ item.item }}"
  delegate_to: localhost
  run_once: true
