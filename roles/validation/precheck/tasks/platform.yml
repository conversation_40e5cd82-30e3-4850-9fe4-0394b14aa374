- name: Check system platform
  vars:
    os_id: "{{ ansible_distribution | lower }}-{{ ansible_distribution_version | lower | replace('/', '_') }}"
  ansible.builtin.assert:
    that:
      - ansible_architecture in platform_gate
      - os_id in platform_gate[ansible_architecture]
      - ansible_kernel in platform_gate[ansible_architecture][os_id]
    fail_msg: "The node {{ inventory_hostname }}({{ ansible_architecture }}-{{ ansible_kernel }}-{{ ansible_distribution }}-{{ ansible_distribution_version }}) is not supported."