---
- name: Check system resources
  block:
    - name: Check available memory
      ansible.builtin.assert:
        that:
          - ansible_memtotal_mb >= min_memory_mb
        fail_msg: "Node {{ inventory_hostname }} has insufficient memory ({{ ansible_memtotal_mb }}MB). Minimum required is {{ min_memory_mb | int // 1024 }}GB."

    - name: Check available CPU cores
      ansible.builtin.assert:
        that:
          - ansible_processor_vcpus >= min_cpu_cores
        fail_msg: "Node {{ inventory_hostname }} has insufficient CPU cores ({{ ansible_processor_vcpus }}). Minimum required is {{ min_cpu_cores }} cores."

    - name: Check available disk space
      ansible.builtin.command: df -h / --output=avail
      register: root_disk_space
      changed_when: false

    - name: Verify disk space
      ansible.builtin.assert:
        that:
          - root_disk_space.stdout_lines[1] | regex_replace('G','') | float >= min_disk_space_gb
        fail_msg: "Node {{ inventory_hostname }} has insufficient disk space. Minimum required is {{ min_disk_space_gb }}GB."

- name: Check system parameters
  block:
    - name: Get max file descriptors
      ansible.builtin.shell: bash -c "ulimit -n"
      register: max_file_descriptors
      changed_when: false

    - name: Verify max file descriptors
      ansible.builtin.assert:
        that:
          - max_file_descriptors.stdout | int >= min_file_descriptors
        fail_msg: "Max file descriptors ({{ max_file_descriptors.stdout }}) is too low. Minimum required is {{ min_file_descriptors }}."

    - name: Check max processes
      ansible.builtin.shell: bash -c "ulimit -u"
      register: max_processes
      changed_when: false

    - name: Verify max processes
      ansible.builtin.assert:
        that:
          - max_processes.stdout | int >= min_processes
        fail_msg: "Max processes ({{ max_processes.stdout }}) is too low. Minimum required is {{ min_processes }}."

- name: Check firewall service status
  ansible.builtin.command: systemctl is-active {{ item }}
  register: firewall_status
  with_items:
    - ufw
  changed_when: false
  failed_when: false
