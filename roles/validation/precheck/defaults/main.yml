---
# Default package versions and architectures
# Based on packages/README.md

# Path to the packages directory relative to the playbook root
# You can override this variable if your packages are in a different location.
packages_local_path: "{{ playbook_dir }}/packages"

# List of required packages constructed from the variables above
required_packages:
  - "{{ metax_driver_file_name }}"
  - "{{ maca_sdk_file_name }}"
  - "{{ mxspm_file_name }}"
  - "{{ metax_gpu_k8s_package_file_name }}"
  - "{{ mx_exporter_file_name }}"

platform_gate:
  x86_64:
    ubuntu-22.04:
      - 5.19.0-46-generic
      - 5.15.0-119-generic
      - 5.15.0-112-generic
      - 5.15.0-100-generic
      - 5.15.0-88-generic
      - 5.15.0-78-generic
      - 5.15.0-72-generic
      - 5.15.0-25-generic
    ubuntu-20.04:
      - 5.15.0-58-generic
      - 5.15.0-46-generic
      - 5.4.0-176-generic
      - 5.4.0-42-generic
      - 5.4.0-26-generic

# System resources requirements
min_memory_mb: 8192  # Minimum required memory in MB
min_cpu_cores: 4    # Minimum required CPU cores
min_disk_space_gb: 20  # Minimum required disk space in GB

# System parameters requirements
min_file_descriptors: 1024  # Minimum required max file descriptors
min_processes: 31669  # Minimum required max processes
