---
# RDMA network interfaces configuration
# Each interface configuration should include:
# - interface: network interface name
# - ipv4_ip: IPv4 address with CIDR notation
# - ipv4_gateway: IPv4 gateway address
# - mtu: Maximum Transmission Unit
# - enable_policy_route: enable policy routing (true/false)

# rdma_network_interfaces:
# - interface: "eno3np2"
#   ipv4_ip: "**********0/24"
#   ipv4_gateway: "**********"
#   mtu: "4200"
#   enable_policy_route: true
# - interface: "eno4np2"
#   ipv4_ip: "***********/24"
#   ipv4_gateway: "**********"
#   mtu: "4200"
#   enable_policy_route: true
rdma_network_interfaces: []

# RDMA QoS configuration for lossless network
# GPU_RDMA_PRIORITY: RDMA traffic priority queue (0-7, default: 5)
# GPU_CNP_PRIORITY: CNP packet priority queue (0-7, default: 6)

rdma_qos_priority: "5"
cnp_qos_priority: "6"

# Reset all RDMA network configurations
# If set to true, it will reset all RDMA configurations including QoS, NIC modes
# and exclusive mode. This is useful when re-provisioning the network.
reset_all_network_config: false
