---
- name: Stop and disable RDMA QoS service
  systemd:
    name: rdma-qos.service
    state: stopped
    enabled: false
  become: true
  failed_when: false

- name: Remove RDMA QoS service file
  file:
    path: /etc/systemd/system/rdma-qos.service
    state: absent
  become: true

- name: Remove RDMA QoS script
  file:
    path: /tmp/scripts/rdma-qos.sh
    state: absent
  become: true

- name: Reload systemd daemon after service file removal
  systemd:
    daemon_reload: true
  become: true

- name: Get all RDMA capable network interfaces (PFs)
  ansible.builtin.shell: "rdma link | grep 'netdev' | grep -oE 'netdev .*' | awk '{print $2}'"
  register: rdma_nics
  changed_when: false
  check_mode: no

- name: Reset QoS settings for each RDMA NIC
  when: rdma_nics.stdout_lines | length > 0
  block:
  - name: Reset mlnx_qos to trust pcp and disable PFC
    ansible.builtin.command: "mlnx_qos -i {{ item }} --trust=pcp --pfc 0,0,0,0,0,0,0,0"
    loop: "{{ rdma_nics.stdout_lines }}"
    ignore_errors: true

  - name: Disable ECN for all priorities
    ansible.builtin.shell: |
      for i in {0..7}; do
        [ -f /sys/class/net/{{ item }}/ecn/roce_np/enable/$i ] && echo 0 > /sys/class/net/{{ item }}/ecn/roce_np/enable/$i
        [ -f /sys/class/net/{{ item }}/ecn/roce_rp/enable/$i ] && echo 0 > /sys/class/net/{{ item }}/ecn/roce_rp/enable/$i
      done
    loop: "{{ rdma_nics.stdout_lines }}"
    args:
      executable: /bin/bash
    ignore_errors: true

  - name: Reset CNP DSCP to 0
    ansible.builtin.shell: "echo 0 > /sys/class/net/{{ item }}/ecn/roce_np/cnp_dscp"
    loop: "{{ rdma_nics.stdout_lines }}"
    ignore_errors: true

- name: Get all RDMA devices
  ansible.builtin.shell: "rdma link | awk '{print $2}' | awk -F'/' '{print $1}' | sort -u"
  register: rdma_devs
  changed_when: false
  check_mode: no

- name: Reset RDMA device QoS settings
  when: rdma_devs.stdout_lines | length > 0
  block:
  - name: Reset cma_roce_tos to 0
    ansible.builtin.command: "cma_roce_tos -d {{ item }} -t 0"
    loop: "{{ rdma_devs.stdout_lines }}"
    ignore_errors: true

  - name: Reset traffic_class to 0
    ansible.builtin.shell: "echo 0 > /sys/class/infiniband/{{ item }}/tc/1/traffic_class"
    loop: "{{ rdma_devs.stdout_lines }}"
    ignore_errors: true

- name: Disable TCP ECN via sysctl
  ansible.builtin.sysctl:
    name: net.ipv4.tcp_ecn
    value: "0"
    state: present

- name: Remove temporary scripts directory
  ansible.builtin.file:
    path: /tmp/scripts
    state: absent

- name: Final verification message
  ansible.builtin.debug:
    msg: "RDMA QoS cleanup process completed. System settings have been reverted to defaults."
