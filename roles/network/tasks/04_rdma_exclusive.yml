---
- name: Check current RDMA netns mode
  ansible.builtin.command: rdma system
  register: rdma_system_output
  changed_when: false
  become: true

- name: Check if RDMA is already in exclusive mode
  set_fact:
    rdma_is_exclusive: "{{ 'netns exclusive' in rdma_system_output.stdout }}"

- name: Configure RDMA exclusive mode (when needed)
  block:
  - name: Add ib_core netns_mode configuration to modprobe
    lineinfile:
      path: /etc/modprobe.d/ib_core.conf
      line: "options ib_core netns_mode=0"
      create: yes
      backup: yes
    become: true

  - name: Ensure rc.local is executable and add command to set RDMA to exclusive mode on boot
    block:
    - name: Ensure /etc/rc.local file exists and has correct permissions
      ansible.builtin.file:
        path: /etc/rc.local
        state: touch
        owner: root
        group: root
        mode: '0755'

    - name: Add shebang to /etc/rc.local if it does not exist
      ansible.builtin.lineinfile:
        path: /etc/rc.local
        regexp: '^#!/bin/sh'
        line: '#!/bin/sh -e'
        insertbefore: BOF

    - name: Add RDMA exclusive command to /etc/rc.local for persistence
      ansible.builtin.lineinfile:
        path: /etc/rc.local
        line: "rdma system set netns exclusive"
        insertbefore: '^exit 0'
        state: present
    become: true

  - name: Set RDMA to exclusive mode
    ansible.builtin.command: rdma system set netns exclusive
    become: true

  # reboot timeout set to 20 minutes (20*60 seconds)
  - name: Reboot system to apply RDMA exclusive mode
    reboot:
      reboot_timeout: 1200
      pre_reboot_delay: 10
      post_reboot_delay: 30
      test_command: uptime
    become: true

  # wait connection timeout set to 5 minutes (5*60 seconds)
  - name: Wait for system to be ready after reboot
    wait_for_connection:
      delay: 10
      timeout: 300

  - name: Verify RDMA exclusive mode after reboot
    ansible.builtin.command: rdma system
    register: rdma_verify_output
    changed_when: false
    retries: 3
    delay: 5
    become: true

  - name: Verify RDMA exclusive mode is properly configured
    assert:
      that:
      - "'netns exclusive' in rdma_verify_output.stdout"
      fail_msg: "RDMA exclusive mode was not properly configured after reboot"
      success_msg: "RDMA exclusive mode successfully configured and verified"

  when: not rdma_is_exclusive
