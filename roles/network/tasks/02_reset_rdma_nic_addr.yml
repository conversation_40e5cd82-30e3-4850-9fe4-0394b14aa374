---
- name: Remove netplan configuration files for RDMA interfaces
  file:
    path: "/etc/netplan/12-{{ item.interface }}.yaml"
    state: absent
  loop: "{{ rdma_network_interfaces }}"
  become: true
  when: rdma_network_interfaces is defined

- name: Apply netplan to remove interface configurations
  ansible.builtin.command: netplan apply
  become: true
  when: rdma_network_interfaces is defined and rdma_network_interfaces | length > 0

- name: Remove custom route dispatcher scripts
  file:
    path: "/etc/networkd-dispatcher/routable.d/{{ item.interface }}-custom-route.sh"
    state: absent
  loop: "{{ rdma_network_interfaces }}"
  become: true
  when: rdma_network_interfaces is defined

- name: Clean up iptables rules for specific RDMA interfaces
  block:
  - name: Get RDMA interface iptables PREROUTING rules
    shell: |
      iptables -t mangle -L PREROUTING --line-numbers -n | grep "ensure_reply_packet_forwarded_by_{{ item.interface }}" | awk '{print $1}' | tac
    register: interface_prerouting_rules
    changed_when: false
    failed_when: false
    become: true
    loop: "{{ rdma_network_interfaces }}"
    when: rdma_network_interfaces is defined

  - name: Remove specific RDMA interface iptables PREROUTING rules
    ansible.builtin.command: "iptables -t mangle -D PREROUTING {{ rule_item }}"
    loop: "{{ interface_prerouting_rules.results | map(attribute='stdout_lines') | flatten }}"
    loop_control:
      loop_var: rule_item
    when:
    - rdma_network_interfaces is defined
    - interface_prerouting_rules.results is defined
    - rule_item | length > 0
    become: true
    failed_when: false

  - name: Get RDMA interface iptables OUTPUT rules
    shell: |
      iptables -t mangle -L OUTPUT --line-numbers -n | grep "ensure_reply_packet_forwarded_by_{{ item.interface }}" | awk '{print $1}' | tac
    register: interface_output_rules
    changed_when: false
    failed_when: false
    become: true
    loop: "{{ rdma_network_interfaces }}"
    when: rdma_network_interfaces is defined

  - name: Remove specific RDMA interface iptables OUTPUT rules
    ansible.builtin.command: "iptables -t mangle -D OUTPUT {{ rule_item }}"
    loop: "{{ interface_output_rules.results | map(attribute='stdout_lines') | flatten }}"
    loop_control:
      loop_var: rule_item
    when:
    - rdma_network_interfaces is defined
    - interface_output_rules.results is defined
    - rule_item | length > 0
    become: true
    failed_when: false

- name: Clean up specific RDMA interface IP rules and routing tables
  block:
  - name: Get fwmark for specific interfaces
    shell: |
      ip rule list | grep "fwmark 0x" | grep "table.*{{ item.interface }}" | sed 's/.*from all fwmark \([^ ]*\) lookup \([^ ]*\).*/\1 \2/'
    register: interface_ip_rules
    changed_when: false
    failed_when: false
    become: true
    loop: "{{ rdma_network_interfaces }}"
    when: rdma_network_interfaces is defined

  - name: Remove specific interface IP rules
    ansible.builtin.command: "ip rule del fwmark {{ rule_parts[0] }} table {{ rule_parts[1] }}"
    vars:
      rule_parts: "{{ rule_item.split() }}"
    loop: "{{ interface_ip_rules.results | map(attribute='stdout_lines') | flatten }}"
    loop_control:
      loop_var: rule_item
    when:
    - rdma_network_interfaces is defined
    - interface_ip_rules.results is defined
    - rule_item | length > 0
    - rule_item.split() | length == 2
    become: true
    failed_when: false

  - name: Flush specific interface routing tables
    ansible.builtin.command: "ip route flush table {{ item.interface }}"
    loop: "{{ rdma_network_interfaces }}"
    when: rdma_network_interfaces is defined
    become: true
    failed_when: false

- name: Remove setNicAddr script
  file:
    path: /tmp/scripts/setNicAddr.sh
    state: absent
  become: true
