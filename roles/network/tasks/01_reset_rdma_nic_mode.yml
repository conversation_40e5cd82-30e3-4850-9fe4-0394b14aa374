---
- name: Check if setNicRdmaMode script exists
  ansible.builtin.stat:
    path: /tmp/scripts/setNicRdmaMode.sh
  register: rdma_script_stat

- name: Ensure scripts directory and script are available
  block:
  - name: Create scripts directory
    ansible.builtin.file:
      path: /tmp/scripts
      state: directory
      mode: '0755'
    become: true

  - name: Copy setNicRdmaMode.sh script to target nodes
    ansible.builtin.copy:
      src: "files/setNicRdmaMode.sh"
      dest: /tmp/scripts/setNicRdmaMode.sh
      mode: '0755'
      owner: root
      group: root
    become: true
  when: not rdma_script_stat.stat.exists

- name: Query current RDMA NIC modes before reset
  ansible.builtin.command: /tmp/scripts/setNicRdmaMode.sh q
  register: initial_rdma_modes_reset
  changed_when: false
  become: true

- name: Reset and verify RDMA NICs to InfiniBand mode
  block:
  - name: Reset RDMA NICs to InfiniBand mode
    environment:
      RDMA_MODE: "infiniband"
    ansible.builtin.command: /tmp/scripts/setNicRdmaMode.sh
    register: rdma_mode_reset_result
    become: true

  - name: Verify RDMA NIC modes after reset
    ansible.builtin.command: /tmp/scripts/setNicRdmaMode.sh q
    register: rdma_nic_verify_result_reset
    changed_when: false
    failed_when: "'ETH' in rdma_nic_verify_result_reset.stdout"
    become: true
  when: "'ETH' in initial_rdma_modes_reset.stdout"

- name: Remove setNicRdmaMode script
  ansible.builtin.file:
    path: /tmp/scripts/setNicRdmaMode.sh
    state: absent
  become: true
