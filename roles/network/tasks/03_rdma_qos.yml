---
- name: Create scripts directory for RDMA QoS
  file:
    path: /tmp/scripts
    state: directory
    mode: '0755'
  become: true

- name: Copy rdma-qos.sh script to target nodes
  copy:
    src: "files/rdma-qos.sh"
    dest: /tmp/scripts/rdma-qos.sh
    mode: '0755'
    owner: root
    group: root
  become: true

- name: Check current RDMA QoS configuration
  ansible.builtin.command: /tmp/scripts/rdma-qos.sh q
  changed_when: false
  failed_when: false
  become: true

- name: Configure RDMA QoS for lossless network
  environment:
    ALL_RDMA_NICS: "true"
    GPU_RDMA_PRIORITY: "{{ rdma_qos_priority | default('5') }}"
    GPU_CNP_PRIORITY: "{{ cnp_qos_priority | default('6') }}"
  ansible.builtin.command: /tmp/scripts/rdma-qos.sh
  become: true

- name: Wait for RDMA QoS configuration to be applied
  pause:
    seconds: 3

- name: Verify RDMA QoS configuration
  ansible.builtin.command: /tmp/scripts/rdma-qos.sh q
  changed_when: false
  become: true

- name: Check RDMA QoS systemd service status
  ansible.builtin.systemd:
    name: rdma-qos.service
  register: rdma_qos_service_status
  become: true

- name: Verify RDMA QoS service is running
  assert:
    that:
    - rdma_qos_service_status.status.ActiveState == "active"
    - rdma_qos_service_status.status.SubState == "running"
    fail_msg: "RDMA QoS service is not running properly. State: {{ rdma_qos_service_status.status.ActiveState }}/{{ rdma_qos_service_status.status.SubState }}"
    success_msg: "RDMA QoS service is running successfully"
