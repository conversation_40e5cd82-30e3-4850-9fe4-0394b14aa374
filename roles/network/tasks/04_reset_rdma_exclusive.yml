---
- name: Check current RDMA netns mode
  ansible.builtin.command: rdma system
  register: current_rdma_system
  changed_when: false
  failed_when: false
  become: true

- name: Check if RDMA is in exclusive mode
  set_fact:
    rdma_is_exclusive: "{{ 'netns exclusive' in current_rdma_system.stdout }}"

- name: Reset RDMA exclusive mode (when needed)
  block:
  - name: Reset RDMA to shared mode
    ansible.builtin.command: rdma system set netns shared
    become: true

  - name: Remove RDMA exclusive command from /etc/rc.local
    ansible.builtin.lineinfile:
      path: /etc/rc.local
      line: "rdma system set netns exclusive"
      state: absent
    become: true

  - name: Remove ib_core modprobe configuration
    file:
      path: /etc/modprobe.d/ib_core.conf
      state: absent
    become: true

  # reboot timeout set to 20 minutes (20*60 seconds)
  - name: Reboot system to apply RDMA reset configuration
    reboot:
      reboot_timeout: 1200
      pre_reboot_delay: 10
      post_reboot_delay: 30
      test_command: uptime
    become: true

  # wait connection timeout set to 5 minutes (5*60 seconds)
  - name: Wait for system to be ready after reboot
    wait_for_connection:
      delay: 10
      timeout: 300

  - name: Verify RDMA shared mode after reset
    ansible.builtin.command: rdma system
    register: rdma_verify_output
    changed_when: false
    retries: 3
    delay: 5

  - name: Verify RDMA shared mode is properly configured
    assert:
      that:
      - "'netns shared' in rdma_verify_output.stdout"
      fail_msg: "RDMA shared mode was not properly configured after reset"
      success_msg: "RDMA exclusive mode successfully reset to shared mode"

  when: rdma_is_exclusive
