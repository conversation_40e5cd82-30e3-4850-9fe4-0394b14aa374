---
- name: Create scripts directory
  file:
    path: /tmp/scripts
    state: directory
    mode: '0755'
  become: true

- name: Copy setNicAddr.sh script to target nodes
  copy:
    src: "files/setNicAddr.sh"
    dest: /tmp/scripts/setNicAddr.sh
    mode: '0755'
    owner: root
    group: root
  become: true

- name: Validate RDMA network interface configuration
  assert:
    that:
    - item.interface is defined and item.interface != ""
    - item.ipv4_ip is defined and item.ipv4_ip != ""
    - item.ipv4_gateway is defined and item.ipv4_gateway != ""
    - item.mtu is defined and item.mtu | int > 0
    - item.enable_policy_route is defined
    fail_msg: "Invalid configuration for interface {{ item.interface | default('unknown') }}"
    success_msg: "Configuration for interface {{ item.interface }} is valid"
  loop: "{{ rdma_network_interfaces[inventory_hostname] }}"
  when: rdma_network_interfaces[inventory_hostname] is defined

- name: Configure RDMA network interfaces
  environment:
    INTERFACE: "{{ item.interface }}"
    IPV4_IP: "{{ item.ipv4_ip }}"
    IPV4_GATEWAY: "{{ item.ipv4_gateway }}"
    MTU: "{{ item.mtu }}"
    ENABLE_POLICY_ROUTE: "{{ item.enable_policy_route | string | lower }}"
  ansible.builtin.command: /tmp/scripts/setNicAddr.sh
  become: true
  loop: "{{ rdma_network_interfaces[inventory_hostname] }}"
  when: rdma_network_interfaces[inventory_hostname] is defined

- name: Wait for network interfaces to be ready
  pause:
    seconds: 5
  when: rdma_network_interfaces[inventory_hostname] is defined and rdma_network_interfaces[inventory_hostname] | length > 0

- name: Verify RDMA network interface configuration
  ansible.builtin.command: "ip addr show {{ item.interface }}"
  changed_when: false
  become: true
  loop: "{{ rdma_network_interfaces[inventory_hostname] }}"
  when: rdma_network_interfaces[inventory_hostname] is defined

- name: Check IP rules for policy routing
  ansible.builtin.command: ip rule list
  changed_when: false
  become: true
  when: rdma_network_interfaces[inventory_hostname] is defined and rdma_network_interfaces[inventory_hostname] | length > 0
