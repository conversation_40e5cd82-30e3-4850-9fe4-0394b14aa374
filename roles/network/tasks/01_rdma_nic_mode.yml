---
- name: Create scripts directory
  ansible.builtin.file:
    path: /tmp/scripts
    state: directory
    mode: '0755'
  become: true

- name: Copy setNicRdmaMode.sh script to target nodes
  ansible.builtin.copy:
    src: "files/setNicRdmaMode.sh"
    dest: /tmp/scripts/setNicRdmaMode.sh
    mode: '0755'
    owner: root
    group: root
  become: true

- name: Query current RDMA NIC modes
  ansible.builtin.command: /tmp/scripts/setNicRdmaMode.sh q
  register: initial_rdma_modes
  changed_when: false
  become: true

- name: Configure and verify RDMA NICs to RoCE mode
  block:
  - name: Configure RDMA NICs to RoCE mode
    environment:
      RDMA_MODE: "roce"
    ansible.builtin.command: /tmp/scripts/setNicRdmaMode.sh
    register: rdma_mode_config_result
    become: true

  - name: Verify RDMA NIC modes after configuration
    ansible.builtin.command: /tmp/scripts/setNicRdmaMode.sh q
    register: rdma_nic_verify_result
    changed_when: false
    failed_when: "'IB' in rdma_nic_verify_result.stdout"
    become: true
  when: "'IB' in initial_rdma_modes.stdout"
