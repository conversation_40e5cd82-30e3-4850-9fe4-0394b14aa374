
BUILD_ARCH= linux/amd64,linux/arm64
TARGET_ARCH ?= $(shell uname -m)
PYTHON_VERSION = 3.12.11

PACKAGE_NAME = korca-$(VERSION)-linux-$(call normalize_arch,$(TARGET_ARCH)).tgz

# Images management
REGISTRY_SERVER_ADDRESS?="release-ci.daocloud.io"
REGISTRY_REPO?="$(REGISTRY_SERVER_ADDRESS)/korca"

# Set your version by env or using latest tags from git
VERSION?=""
ifeq ($(VERSION), "")
	# The official tag version will be passed in from the environment variable. If the development version is not passed in, mark the dev tag
    LATEST_DEV_TAG=$(shell ./hack/release/get-version.sh korca)-dev-$(shell git rev-parse --short=8 HEAD)
    ifeq ($(LATEST_DEV_TAG),)
        # Forked repo may not sync tags from upstream, so give it a default tag to make <PERSON><PERSON> happy.
        VERSION="unknown"
    else
        VERSION=$(LATEST_DEV_TAG)
    endif
endif

# convert to git version to semver version v0.1.1-14-gb943a40 --> v0.1.1+14-gb943a40
KORCA_VERSION := $(shell echo $(VERSION) | sed 's/-/+/1')
#KORCA_VERSION := "v0.40.0+dev-b608ab17"

# convert to git version to semver version v0.1.1+14-gb943a40 --> v0.1.1-14-gb943a40
KORCA_IMAGE_VERSION := $(shell echo $(KORCA_VERSION) | sed 's/+/-/1')
#KORCA_IMAGE_VERSION := "v0.40.0-dev-b608ab17"

all: image

.PHONY: docker-login
docker-login:docker-login
	@echo "push images to $(REGISTRY_REPO)"
	echo ${REGISTRY_PASSWORD} | docker login ${REGISTRY_SERVER_ADDRESS} -u ${REGISTRY_USER_NAME} --password-stdin

.PHONY: image
image: $(SOURCES) docker-login
	echo "Building korca for arch = $(BUILD_ARCH)"
	export DOCKER_CLI_EXPERIMENTAL=enabled ;\
	! ( docker buildx ls | grep korca-multi-platform-builder ) && docker buildx create --use --platform=$(BUILD_ARCH) --name korca-multi-platform-builder --driver-opt image=docker.m.daocloud.io/moby/buildkit:buildx-stable-1 ;\
	docker buildx build \
			--builder korca-multi-platform-builder \
			--platform $(BUILD_ARCH) \
			--tag $(REGISTRY_REPO)/korca-job:$(KORCA_IMAGE_VERSION)  \
			--tag $(REGISTRY_REPO)/korca-job:latest  \
			-f ./Dockerfile \
			--push \
			.

.PHONY: setup-ansible
setup-ansible:
	./hack/setup-ansible.sh $(PYTHON_VERSION) $(TARGET_ARCH) $(shell pwd)

.PHONY: package
package: setup-ansible
	$(eval PACKAGE_STORE_DIR ?= $(shell pwd)/pack)
	./hack/make-package.sh $(PACKAGE_NAME) $(shell pwd) $(PACKAGE_STORE_DIR)

.PHONY: release-version
release-version:
	hack/release/release.sh

define normalize_arch
$(if $(filter x86_64,$(1)),amd64,$(if $(filter aarch64,$(1)),arm64,$(1)))
endef

define canonicalize_arch
$(if $(filter amd64,$(1)),x86_64,$(if $(filter arm64,$(1)),aarch64,$(1)))
endef