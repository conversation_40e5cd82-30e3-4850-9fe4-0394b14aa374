---
- name: Deploy GPU Cluster Extensions
  hosts: all
  gather_facts: true
  become: true

  tasks:
  - name: Display extension deployment start message
    debug:
      msg: "Starting extension deployment - Host: {{ inventory_hostname }}"

  - name: Deploy metax GPU extensions
    include_role:
      name: extensions/metax_gpu_extensions
    tags: [ extensions, metax_gpu ]

  - name: Deploy metax mx exporter
    include_role:
      name: extensions/metax-mx-exporter
    tags: [ extensions, mx_exporter ]

  - name: Deploy SpiderPool
    include_role:
      name: extensions/spiderpool
    tags: [ extensions, spiderpool ]

  - name: Deploy Azure Lustre CSI Driver
    include_role:
      name: extensions/azurelustre_csi_driver
    tags: [ extensions, azurelustre_csi_driver ]

  - name: Deploy monitoring stack
    include_role:
      name: extensions/monitoring
    tags: [ extensions, monitoring ]

  - name: Run validation checks
    include_role:
      name: validation/postcheck
    tags: [ postcheck ]

  - name: Display extension deployment completion message
    debug:
      msg: "Extension deployment completed - Host: {{ inventory_hostname }}"
