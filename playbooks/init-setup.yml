---
- name: GPU Cluster Initial Setup
  hosts: control_plane_nodes,gpu_nodes
  gather_facts: true
  become: true

  tasks:
  - name: Display cluster initialization start message
    debug:
      msg: "Starting GPU cluster initialization - Host: {{ inventory_hostname }}"

  - name: Pre-check system requirements
    include_role:
      name: validation/precheck
    tags: [ precheck ]

  - name: Execute common configuration
    include_role:
      name: common
    tags: [ common ]

  - name: Configure network settings
    include_role:
      name: network
    tags: [ network ]

  - name: Install GPU/Metax role
    include_role:
      name: gpu/metax
    tags: [ gpu, metax ]

  - name: Run metalink training test
    include_role:
      name: validation/metalink
    tags: [ metalink ]

  - name: Display initialization completion message
    debug:
      msg: "GPU cluster initialization completed - Host: {{ inventory_hostname }}"
