# Korca

Korca is a GPU cluster deployer tool that automates the setup and configuration of a GPU cluster.

It uses Ansible playbooks to perform the deployment tasks.

Kubernetes cluster deployment is outside the scope of Korca and should be performed using kubean or other Cluster Lifecycle Management (LCM) tools.

## Prerequisites

*   Python == 3.12

## Setup

### Initialize Virtual Environment

```bash
source .venv/bin/activate
```

### Distribute SSH Keys (Skip if already done)

```bash
# 1. Generate SSH key pair
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f $HOME/.ssh/id_rsa

# 2. Copy SSH public key to all nodes
IPS=(************* *************)
PASSWORD="your_password"
for ip in ${IPS[@]}; do
  sshpass -p "$PASSWORD" ssh-copy-id -i ~/.ssh/id_rsa.pub -o StrictHostKeyChecking=no root@$ip
done
```

## Configuration

### Edit Inventory Files

```bash
vi inventory/inventory.ini
vi inventory/group_vars/all/all.yml
vi inventory/group_vars/all/setup.yml
vi inventory/group_vars/all/extensions.yml
```

## Deployment

### Run Playbooks

```bash
# Test connection
ansible -i inventory/inventory.ini all -m ping -v

# Initial setup
ansible-playbook -i inventory/inventory.ini init-setup.yml

# Deploy extensions
ansible-playbook -i inventory/inventory.ini deploy-extensions.yml
```

## Airgap Deployment

1. Get or [build](./docs/package.md) the package
2. [Extract the package and run the playbooks](./docs/startup.md)
